{"name": "@cscs-agent/cli", "version": "0.3.0", "description": "CLI tool for creating CSCS Agent projects from templates", "type": "module", "files": ["bin", "lib", "templates", "README.md"], "bin": "./bin/cli.js", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "vitest run", "test:watch": "vitest", "test:cli": "node bin/cli.js --help", "lint": "eslint src --ext .ts,.js --fix"}, "dependencies": {"@modelcontextprotocol/server-filesystem": "^2025.3.28", "@openai/agents": "^0.0.10", "chalk": "^5.3.0", "commander": "^12.1.0", "diff": "^8.0.2", "dotenv": "^17.0.1", "fs-extra": "^11.2.0", "gradient-string": "^3.0.0", "inquirer": "^12.1.0", "openai": "^5.8.2", "ora": "^8.1.1"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/node": "^22.15.0", "eslint": "^9.25.1", "jsdom": "^26.1.0", "typescript": "^5.8.2", "vitest": "^2.1.9"}, "keywords": ["cli", "template", "cscs-agent", "project-generator"], "author": "CSCS Team", "license": "ISC", "packageManager": "pnpm@10.9.0", "engines": {"node": ">=18.0.0"}}