/**
 * Tests for ConfigUpdater with LLM integration using @openai/agents
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import type { WidgetGenerationOptions } from "../../types.js";
import { ConfigUpdater } from "../config-updater.js";

// Mock the FileSystemManager
vi.mock("../filesystem-manager.js", () => ({
  FileSystemManager: vi.fn().mockImplementation(() => ({
    fileExists: vi.fn(),
    readFile: vi.fn(),
    writeFile: vi.fn(),
    updateFile: vi.fn(),
  })),
}));

// Mock the Logger
vi.mock("../logger.js", () => ({
  Logger: {
    info: vi.fn(),
    warning: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock @openai/agents
vi.mock("@openai/agents", () => ({
  Agent: vi.fn().mockImplementation(() => ({})),
  run: vi.fn(),
}));

// Mock inquirer
vi.mock("inquirer", () => ({
  default: {
    prompt: vi.fn(),
  },
}));

// Mock chalk
vi.mock("chalk", () => ({
  default: {
    bold: vi.fn((text) => text),
    green: vi.fn((text) => text),
    red: vi.fn((text) => text),
    gray: vi.fn((text) => text),
  },
}));

describe("ConfigUpdater", () => {
  let configUpdater: ConfigUpdater;
  let mockFsManager: any;
  let mockRun: any;
  let mockInquirer: any;

  const sampleOptions: WidgetGenerationOptions = {
    name: "TestWidget",
    type: "basic",
    targetPath: "/test/path",
    description: "A test widget",
    placement: "message",
    slot: "blocks",
  };

  const sampleExistingConfig = `import type { AgentChatConfig } from "@cscs-agent/core";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Test Agent",
      code: "test-agent",
      description: "A test agent",
      message: {
        blocks: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};`;

  beforeEach(async () => {
    // Import the mocked modules
    const { run } = await import("@openai/agents");
    const inquirer = await import("inquirer");
    mockRun = run as any;
    mockInquirer = inquirer.default as any;

    configUpdater = new ConfigUpdater();

    // Create mock methods
    mockFsManager = {
      fileExists: vi.fn(),
      readFile: vi.fn(),
      writeFile: vi.fn(),
      updateFile: vi.fn(),
    };

    // Replace the fsManager instance
    (configUpdater as any).fsManager = mockFsManager;

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("updateAgentConfig", () => {
    it("should create new config file when it does not exist", async () => {
      mockFsManager.fileExists.mockReturnValue(false);

      await configUpdater.updateAgentConfig(
        "/test/agent-config.tsx",
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(mockFsManager.writeFile).toHaveBeenCalledWith(
        "/test/agent-config.tsx",
        expect.stringContaining('import TestWidget from "./widgets/testwidget"'),
      );
    });

    it("should use LLM to update existing config with diff confirmation", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock successful LLM response from @openai/agents
      const mockLLMResponse = `import type { AgentChatConfig } from "@cscs-agent/core";
import TestWidget from "./widgets/testwidget";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Test Agent",
      code: "test-agent",
      description: "A test agent",
      message: {
        blocks: {
          widgets: [
            {
              code: "@Custom/TestWidget",
              component: TestWidget,
            }
          ],
        },
      },
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};`;

      mockRun.mockResolvedValue({
        finalOutput: mockLLMResponse,
      });

      // Mock user confirming changes
      mockInquirer.prompt.mockResolvedValue({ choice: "confirm" });

      await configUpdater.updateAgentConfig(
        "/test/agent-config.tsx",
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(mockRun).toHaveBeenCalledWith(
        expect.any(Object), // The agent instance
        expect.stringContaining("Update the existing agent configuration file"),
      );

      expect(mockInquirer.prompt).toHaveBeenCalledWith([
        expect.objectContaining({
          type: "list",
          name: "choice",
          message: "Review the changes above. What would you like to do?",
        }),
      ]);

      expect(mockFsManager.updateFile).toHaveBeenCalledWith(
        "/test/agent-config.tsx",
        expect.stringContaining('import TestWidget from "./widgets/testwidget"'),
        true,
      );
    });

    it("should revert changes when user chooses to revert", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock successful LLM response
      const mockLLMResponse = `import type { AgentChatConfig } from "@cscs-agent/core";
import TestWidget from "./widgets/testwidget";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Test Agent",
      code: "test-agent",
      description: "A test agent",
      message: {
        blocks: {
          widgets: [
            {
              code: "@Custom/TestWidget",
              component: TestWidget,
            }
          ],
        },
      },
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};`;

      mockRun.mockResolvedValue({
        finalOutput: mockLLMResponse,
      });

      // Mock user choosing to revert changes
      mockInquirer.prompt.mockResolvedValue({ choice: "revert" });

      await configUpdater.updateAgentConfig(
        "/test/agent-config.tsx",
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(mockRun).toHaveBeenCalled();
      expect(mockInquirer.prompt).toHaveBeenCalled();

      // File should not be updated when user reverts
      expect(mockFsManager.updateFile).not.toHaveBeenCalled();
    });

    it("should handle no changes detected", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock LLM returning the same content (no changes)
      mockRun.mockResolvedValue({
        finalOutput: sampleExistingConfig,
      });

      await configUpdater.updateAgentConfig(
        "/test/agent-config.tsx",
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(mockRun).toHaveBeenCalled();

      // Should not prompt user when no changes detected
      expect(mockInquirer.prompt).not.toHaveBeenCalled();
      expect(mockFsManager.updateFile).not.toHaveBeenCalled();
    });

    it("should throw error when LLM fails", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock failed LLM response
      mockRun.mockRejectedValue(new Error("LLM API Error"));

      await expect(
        configUpdater.updateAgentConfig(
          "/test/agent-config.tsx",
          sampleOptions,
          "./widgets/testwidget",
          "@Custom/TestWidget",
        ),
      ).rejects.toThrow("LLM API Error");

      expect(mockRun).toHaveBeenCalled();
      expect(mockFsManager.updateFile).not.toHaveBeenCalled();
    });

    it("should throw error when LLM returns invalid response", async () => {
      mockFsManager.fileExists.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(sampleExistingConfig);

      // Mock LLM response with no finalOutput
      mockRun.mockResolvedValue({
        finalOutput: null,
      });

      await expect(
        configUpdater.updateAgentConfig(
          "/test/agent-config.tsx",
          sampleOptions,
          "./widgets/testwidget",
          "@Custom/TestWidget",
        ),
      ).rejects.toThrow("LLM did not return valid configuration content");

      expect(mockRun).toHaveBeenCalled();
      expect(mockFsManager.updateFile).not.toHaveBeenCalled();
    });
  });

  describe("generateNewAgentConfig", () => {
    it("should generate correct config for message placement", () => {
      const result = (configUpdater as any).generateNewAgentConfig(
        sampleOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(result).toContain('import TestWidget from "./widgets/testwidget"');
      expect(result).toContain('code: "@Custom/TestWidget"');
      expect(result).toContain("component: TestWidget");
      expect(result).toContain("message: {");
      expect(result).toContain("blocks: {");
    });

    it("should generate correct config for sidePanel placement", () => {
      const sidePanelOptions = { ...sampleOptions, placement: "sidePanel" as const };

      const result = (configUpdater as any).generateNewAgentConfig(
        sidePanelOptions,
        "./widgets/testwidget",
        "@Custom/TestWidget",
      );

      expect(result).toContain("sidePanel: {");
      expect(result).toContain("render: {");
    });
  });

  describe("diff functionality", () => {
    it("should generate diff correctly", () => {
      const originalContent = `line 1
line 2
line 3`;
      const modifiedContent = `line 1
modified line 2
line 3
new line 4`;

      const diffResult = (configUpdater as any).generateDiff(
        "/test/file.ts",
        originalContent,
        modifiedContent,
      );

      expect(diffResult.filePath).toBe("/test/file.ts");
      expect(diffResult.hasChanges).toBe(true);
      expect(diffResult.diffLines.length).toBeGreaterThan(0);

      // Check that we have the expected diff structure
      const addedLines = diffResult.diffLines.filter((line: any) => line.type === "added");
      const removedLines = diffResult.diffLines.filter((line: any) => line.type === "removed");
      const contextLines = diffResult.diffLines.filter((line: any) => line.type === "context");

      expect(addedLines.length).toBeGreaterThan(0);
      expect(removedLines.length).toBeGreaterThan(0);
      expect(contextLines.length).toBeGreaterThan(0);
    });

    it("should detect no changes when content is identical", () => {
      const content = `line 1
line 2
line 3`;

      const diffResult = (configUpdater as any).generateDiff(
        "/test/file.ts",
        content,
        content,
      );

      expect(diffResult.hasChanges).toBe(false);
      expect(diffResult.diffLines.every((line: any) => line.type === "context")).toBe(true);
    });
  });
});
